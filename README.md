# Jest - Sports Training Tracker

A modern sports training attendance and progress tracking application built with Next.js, Clerk authentication, and chadcn/ui components.

## Features

- 🔐 **Authentication**: Secure user authentication with Clerk
- 📊 **Dashboard**: Comprehensive dashboard with training statistics
- 📅 **Session Management**: Create and manage training sessions
- 👥 **Athlete Management**: Track athlete enrollment and attendance
- 📈 **Analytics**: View training progress and attendance analytics
- 🎨 **Modern UI**: Built with chadcn/ui components and Tailwind CSS
- 📱 **Responsive**: Mobile-first responsive design

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Authentication**: Clerk
- **UI Components**: chadcn/ui (shadcn/ui)
- **Styling**: Tailwind CSS v4
- **Icons**: Lucide React
- **Language**: TypeScript

## Database Structure

Currently, the application uses Clerk for user management and authentication. The planned database structure includes:

### Users

- Managed by Clerk authentication
- User profiles with first name, last name, email
- Role-based access (coaches, administrators)

### Training Sessions

```sql
-- Planned structure
sessions {
  id: string (primary key)
  name: string
  description: text
  date: datetime
  start_time: time
  end_time: time
  max_participants: integer
  coach_id: string (foreign key to users)
  created_at: datetime
  updated_at: datetime
}
```

### Athletes

```sql
-- Planned structure
athletes {
  id: string (primary key)
  first_name: string
  last_name: string
  email: string
  phone: string
  date_of_birth: date
  emergency_contact: string
  emergency_phone: string
  created_at: datetime
  updated_at: datetime
}
```

### Attendance

```sql
-- Planned structure
attendance {
  id: string (primary key)
  session_id: string (foreign key to sessions)
  athlete_id: string (foreign key to athletes)
  status: enum ('present', 'absent', 'late')
  check_in_time: datetime
  notes: text
  created_at: datetime
  updated_at: datetime
}
```

### Session Enrollments

```sql
-- Planned structure
session_enrollments {
  id: string (primary key)
  session_id: string (foreign key to sessions)
  athlete_id: string (foreign key to athletes)
  enrolled_at: datetime
}
```

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Clerk account for authentication

### Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd safe-panel
```

2. Install dependencies:

```bash
npm install
```

3. Set up environment variables:

```bash
cp .env.example .env.local
```

Add your Clerk keys to `.env.local`:

```
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key
```

4. Run the development server:

```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── dashboard/         # Dashboard pages and layout
│   ├── sign-in/          # Authentication pages
│   ├── sign-up/
│   ├── globals.css       # Global styles
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Home page
├── components/
│   └── ui/               # chadcn/ui components
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
└── middleware.ts         # Clerk middleware for route protection
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## Authentication & Security

- Protected routes using Clerk middleware
- Dashboard routes require authentication
- User session management handled by Clerk
- Secure sign-in/sign-up flows

## UI Components

The application uses chadcn/ui components including:

- Sidebar navigation with collapsible functionality
- Cards for displaying statistics and information
- Buttons with various variants
- Responsive layout components
- Dark/light mode support

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
