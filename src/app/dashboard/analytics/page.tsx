"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Users, Calendar, Clock, Target } from "lucide-react";

export default function AnalyticsPage() {
  const metrics = [
    {
      title: "Monthly Sessions",
      value: "24",
      change: "+12%",
      trend: "up",
      description: "vs last month",
      icon: Calendar,
    },
    {
      title: "Average Attendance",
      value: "87%",
      change: "+3%",
      trend: "up",
      description: "vs last month",
      icon: Users,
    },
    {
      title: "Total Training Hours",
      value: "342",
      change: "+15%",
      trend: "up",
      description: "vs last month",
      icon: Clock,
    },
    {
      title: "Goal Achievement",
      value: "92%",
      change: "-2%",
      trend: "down",
      description: "vs last month",
      icon: Target,
    },
  ];

  const weeklyData = [
    { day: "Mon", sessions: 4, attendance: 85 },
    { day: "Tue", sessions: 3, attendance: 92 },
    { day: "Wed", sessions: 5, attendance: 78 },
    { day: "Thu", sessions: 2, attendance: 95 },
    { day: "Fri", sessions: 4, attendance: 88 },
    { day: "Sat", sessions: 6, attendance: 82 },
    { day: "Sun", sessions: 1, attendance: 90 },
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
        <p className="text-muted-foreground">
          Track performance metrics and training insights
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric) => (
          <Card key={metric.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {metric.title}
              </CardTitle>
              <metric.icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <div className="flex items-center gap-1 text-xs">
                {metric.trend === "up" ? (
                  <TrendingUp className="h-3 w-3 text-green-600" />
                ) : (
                  <TrendingDown className="h-3 w-3 text-red-600" />
                )}
                <span className={metric.trend === "up" ? "text-green-600" : "text-red-600"}>
                  {metric.change}
                </span>
                <span className="text-muted-foreground">{metric.description}</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Weekly Overview */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Weekly Sessions</CardTitle>
            <CardDescription>Number of training sessions per day</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {weeklyData.map((day) => (
                <div key={day.day} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{day.day}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${(day.sessions / 6) * 100}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-muted-foreground w-8">
                      {day.sessions}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Weekly Attendance</CardTitle>
            <CardDescription>Attendance percentage per day</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {weeklyData.map((day) => (
                <div key={day.day} className="flex items-center justify-between">
                  <span className="text-sm font-medium">{day.day}</span>
                  <div className="flex items-center gap-2">
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-green-600 h-2 rounded-full" 
                        style={{ width: `${day.attendance}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-muted-foreground w-8">
                      {day.attendance}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
