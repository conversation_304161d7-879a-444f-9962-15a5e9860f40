"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { UserPlus, Mail, Phone, Calendar } from "lucide-react";

export default function AthletesPage() {
  const athletes = [
    {
      id: 1,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      joinDate: "2024-01-10",
      sessionsAttended: 15,
      attendanceRate: "94%",
    },
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      joinDate: "2024-01-08",
      sessionsAttended: 18,
      attendanceRate: "90%",
    },
    {
      id: 3,
      name: "<PERSON>",
      email: "<EMAIL>",
      phone: "+****************",
      joinDate: "2024-01-12",
      sessionsAttended: 12,
      attendanceRate: "86%",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Athletes</h1>
          <p className="text-muted-foreground">
            Manage athlete profiles and track their progress
          </p>
        </div>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Add Athlete
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {athletes.map((athlete) => (
          <Card key={athlete.id}>
            <CardHeader>
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarFallback>
                    {athlete.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-lg">{athlete.name}</CardTitle>
                  <CardDescription>
                    Joined {new Date(athlete.joinDate).toLocaleDateString()}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Mail className="h-4 w-4" />
                {athlete.email}
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Phone className="h-4 w-4" />
                {athlete.phone}
              </div>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Calendar className="h-4 w-4" />
                {athlete.sessionsAttended} sessions attended
              </div>
              <div className="flex items-center justify-between pt-2">
                <span className="text-sm font-medium">Attendance Rate</span>
                <span className="text-sm font-bold text-green-600">
                  {athlete.attendanceRate}
                </span>
              </div>
              <Button variant="outline" className="w-full" size="sm">
                View Profile
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
