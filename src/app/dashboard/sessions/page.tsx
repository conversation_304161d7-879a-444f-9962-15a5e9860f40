"use client";

import { <PERSON>, CardContent, CardD<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, Plus, Users, Clock } from "lucide-react";

export default function SessionsPage() {
  const sessions = [
    {
      id: 1,
      name: "Morning Basketball",
      date: "2024-01-15",
      time: "8:00 AM - 10:00 AM",
      participants: "12/15",
      status: "Scheduled",
    },
    {
      id: 2,
      name: "Evening Soccer",
      date: "2024-01-15",
      time: "6:00 PM - 8:00 PM",
      participants: "18/20",
      status: "In Progress",
    },
    {
      id: 3,
      name: "Swimming Practice",
      date: "2024-01-16",
      time: "7:00 AM - 9:00 AM",
      participants: "8/10",
      status: "Scheduled",
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Training Sessions</h1>
          <p className="text-muted-foreground">
            Manage and track your training sessions
          </p>
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          New Session
        </Button>
      </div>

      <div className="grid gap-4">
        {sessions.map((session) => (
          <Card key={session.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    {session.name}
                  </CardTitle>
                  <CardDescription>
                    {session.date} • {session.time}
                  </CardDescription>
                </div>
                <div className="text-right">
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <Users className="h-4 w-4" />
                    {session.participants}
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    session.status === 'Scheduled' ? 'bg-blue-100 text-blue-800' :
                    session.status === 'In Progress' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {session.status}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Clock className="mr-2 h-4 w-4" />
                  View Details
                </Button>
                <Button variant="outline" size="sm">
                  <Users className="mr-2 h-4 w-4" />
                  Manage Attendance
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
